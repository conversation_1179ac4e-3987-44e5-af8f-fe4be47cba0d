import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const EarthTesterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'ca-6424', model: 'CA 6424', subtitle: '2P/3P Earth Tester' },
    { id: 'ca-6460', model: 'CA 6460', subtitle: '4P Earth Tester' },
    { id: 'ca-6470n', model: 'CA 6470N', subtitle: '3P/4P Earth Tester' },
    { id: 'ca-6472', model: 'CA 6472', subtitle: 'Advanced 3P/4P Earth Tester' }
  ];

  // Complete product data
  const productData = {
    'ca-6424': {
      id: 'ca-6424',
      model: 'CA 6424',
      subtitle: '2P/3P Earth Tester',
      image: '/earth testers/CA 6424.png',
      images: [
        '/earth testers/CA 6424.png'
      ],
      voltage: 'CAT III 600V',
      measurement: '2P/3P Earth Testing',
      accuracy: '±2% + 1 count',
      price: 'Contact for pricing',
      description: 'Professional earth resistance tester with backlit LCD for 2-pole and 3-pole measurements. Ideal for ground system verification and safety testing.',
      keyFeatures: [
        'Backlit custom 206-segment LCD display',
        'Auto Power off for battery conservation',
        'Noise indication for interference detection',
        'Measurement Mode: V, I, R 2P (Ω), R 3P (Ω)',
        'Compact and portable design',
        'Easy to use interface',
        'Accurate measurements up to 50 kΩ',
        'Voltage measurement up to 600V AC',
        'Leakage current measurement',
        'Professional build quality'
      ],
      technicalSpecs: {
        'Model': 'CA 6424 2P/3P Earth Tester',
        'Voltage Measurement': 'Up to 600 V AC',
        '2P Earth Resistance': '0.05 Ω to 50 kΩ',
        '2P Accuracy': '±(2% R + 1 count)',
        '3P Earth Resistance': '0.5 Ω to 50.00 kΩ',
        '3P Accuracy': '±(2% R + 1 count)',
        'RH Stake Resistance': '0.05 Ω to 49.99 kΩ',
        'U₀ Voltage Measurement': 'Up to 600 VAC',
        'Leakage Current': 'Up to 60.00 A',
        'Display': 'Backlit custom 206-segment LCD',
        'Power Management': 'Auto Power off',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Safety Rating': 'CAT III 600V',
        'Weight': 'Approximately 1.2kg'
      },
      applications: [
        'Ground system verification',
        'Lightning protection system testing',
        'Utility and substation testing',
        'Construction site safety',
        'Electrical installation verification',
        'Equipment grounding assessment'
      ],
      advantages: [
        'Compact and portable design',
        'Easy to use interface',
        'Accurate measurements',
        'Durable construction',
        'Auto power-off feature',
        'Noise indication capability'
      ]
    },
    'ca-6460': {
      id: 'ca-6460',
      model: 'CA 6460',
      subtitle: '4P Earth Tester',
      image: '/earth testers/CA 6460.png',
      images: [
        '/earth testers/CA 6460.png',
        '/earth testers/CA 6462.png'
      ],
      voltage: 'CAT III 600V',
      measurement: '4P Earth Testing',
      accuracy: '±2% ± 1 point',
      price: 'Contact for pricing',
      description: 'Advanced 4-pole earth resistance and resistivity tester with digital display for precise measurements. Features multiple test methods for comprehensive analysis.',
      keyFeatures: [
        'Large backlit digital display with 2,000 counts',
        '3 fault presence indicators to validate measurement',
        'Battery status indicator',
        'Non Rechargeable/Rechargeable Batteries option',
        'Professional grade accuracy',
        'Multiple test methods support',
        'Robust field case included',
        'Temperature compensation',
        'Data hold function',
        'Wenner method compatibility'
      ],
      technicalSpecs: {
        'Model': 'CA 6460 4P Earth Tester',
        'Resistance Range': '0.01 to 2,000 Ω (3 automatic ranges)',
        'Test Current': '10mA, 1mA, 0.1mA',
        'Accuracy': '±2% ±1 point',
        'Frequency': '128Hz',
        'Resistivity Method': 'Wenner method (4-rod method)',
        'Ground Resistance Method': 'TAGG method (62% method)',
        'Display': 'Large backlit digital display, 2,000 counts',
        'Fault Indicators': '3 fault presence indicators',
        'Battery Type': 'Non Rechargeable/Rechargeable option',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Safety Rating': 'CAT III 600V',
        'Weight': 'Approximately 1.5kg',
        'Accessories': 'Professional field case included'
      },
      applications: [
        'Ground system verification',
        'Industrial plant maintenance',
        'Lightning protection system testing',
        'Utility and substation testing',
        'Soil resistivity measurement',
        'Equipment grounding verification'
      ],
      advantages: [
        'Professional grade accuracy',
        'Multiple test methods available',
        'Robust field case included',
        'Rechargeable battery option',
        'Fault presence indicators',
        'Temperature compensation'
      ]
    },
    'ca-6470n': {
      id: 'ca-6470n',
      model: 'CA 6470N',
      subtitle: '3P/4P Earth Tester',
      image: '/earth testers/CA 6470N.png',
      images: [
        '/earth testers/CA 6470N.png',
        '/earth testers/CA 6471.png'
      ],
      voltage: 'CAT III 600V',
      measurement: '3P/4P Earth Testing',
      accuracy: '±2% + 1 count',
      price: 'Contact for pricing',
      description: 'Multi-function earth and resistivity tester with advanced features for professional use. Features USB communication and extensive memory storage.',
      keyFeatures: [
        'Backlit LCD display featuring 3 simultaneous display levels',
        'Noise interference detection',
        'Alarm function for threshold monitoring',
        'Memory: 512 memory locations',
        'Communication: USB interface',
        'Advanced measurement modes',
        'Professional data logging',
        'Comprehensive analysis tools',
        'User-friendly interface',
        'Rugged construction'
      ],
      technicalSpecs: {
        'Model': 'CA 6470N 3P/4P Earth Tester',
        'Measurement Range': '0.01 Ω to 99.9 kΩ',
        'Frequency Range': '41 to 512 Hz',
        'Selective 4-pole measurements': '2 clamps (CA 6471)',
        'Resistivity Range': '0.01 Ω to 99.9 kΩ',
        'Memory Storage': '512 locations',
        'Communication': 'USB interface',
        'Display': 'Backlit LCD with 3 simultaneous levels',
        'Noise Detection': 'Built-in interference detection',
        'Alarm Function': 'Threshold monitoring capability',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Safety Rating': 'CAT III 600V',
        'Weight': 'Approximately 1.8kg',
        'Data Export': 'USB data transfer'
      },
      applications: [
        'Ground system verification',
        'Industrial plant maintenance',
        'Lightning protection system testing',
        'Utility and substation testing',
        'Research and development',
        'Quality control testing'
      ],
      advantages: [
        'Advanced features and functionality',
        'USB data export capability',
        'Large memory storage (512 locations)',
        'Professional use applications',
        'Noise interference detection',
        'Comprehensive alarm functions'
      ]
    },
    'ca-6472': {
      id: 'ca-6472',
      model: 'CA 6472',
      subtitle: 'Advanced 3P/4P Earth Tester',
      image: '/earth testers/CA 6472.png',
      images: [
        '/earth testers/CA 6472.png',
        '/earth testers/CA 6474 with CA 6472.png'
      ],
      voltage: 'CAT III 600V',
      measurement: 'Advanced 3P/4P Testing',
      accuracy: '±2% + 1 count',
      price: 'Contact for pricing',
      description: 'Professional earth tester with advanced features for pylons and comprehensive earth system analysis. The most advanced model in the series.',
      keyFeatures: [
        'Backlit LCD display featuring 3 simultaneous display levels',
        'Automatic & Expert mode operation',
        'Earth measurement on Pylons with earth cable (with CA 6474 option)',
        'Alarm function for safety monitoring',
        'Memory: 512-record memory storage',
        'Communication: USB interface',
        'Measurement with CA 6474 Range: 0.001 Ω to 99.9 kΩ',
        'Frequency: 41 to 5078 Hz',
        'Professional analysis software',
        'Advanced reporting capabilities'
      ],
      technicalSpecs: {
        'Model': 'CA 6472 Advanced 3P/4P Earth Tester',
        'Measurement Range': '0.01 Ω to 99.9 kΩ',
        'Extended Range (with CA 6474)': '0.001 Ω to 99.9 kΩ',
        'Frequency Range': '41 to 5,078 Hz',
        'Earth Measurement (2 clamps)': '0.01 Ω to 500 Ω',
        'Resistivity Range': '0.01 Ω to 99.9 kΩ',
        'Earth Potential Range': '0.01 mV to 65.00 V',
        'DC Resistance Range': '0.001 Ω to 99.9 kΩ',
        'Memory Storage': '512-record memory',
        'Communication': 'USB interface',
        'Display': 'Backlit LCD with 3 simultaneous levels',
        'Operating Modes': 'Automatic & Expert mode',
        'Special Features': 'Pylon measurement capability',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Safety Rating': 'CAT III 600V'
      },
      applications: [
        'Research and development',
        'Utility and substation testing',
        'Industrial plant maintenance',
        'Lightning protection system testing',
        'Power transmission line testing',
        'Advanced earth system analysis'
      ],
      advantages: [
        'Comprehensive analysis capabilities',
        'Advanced memory and data logging',
        'USB connectivity for data transfer',
        'Professional design and construction',
        'Pylon measurement capability',
        'Expert mode for advanced users'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/earth-testers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Earth Tester`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('tft') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('data') || feature.toLowerCase().includes('logging')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('connectivity') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('communication')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power') && !feature.toLowerCase().includes('measurement')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal') || feature.toLowerCase().includes('compensation')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('resistance')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('frequency') || feature.toLowerCase().includes('range') || feature.toLowerCase().includes('calibration') || feature.toLowerCase().includes('accuracy')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('cat') || feature.toLowerCase().includes('alarm') || feature.toLowerCase().includes('protection') || feature.toLowerCase().includes('fault')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('analysis') || feature.toLowerCase().includes('monitoring') || feature.toLowerCase().includes('reporting') || feature.toLowerCase().includes('software') || feature.toLowerCase().includes('expert') || feature.toLowerCase().includes('automatic')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Earth Testers
              </h1>
              <p className="typography-h4 text-black">
                Professional Earth Testing Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => navigate(`/measure/earth-testers/product/${prod.id}`)}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/earth-testers')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Safety Rating</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{ 
                        maxHeight: '200px',
                        maxWidth: '200px',
                        background: 'transparent',
                        mixBlendMode: 'multiply',
                        filter: 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on earth testing solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EarthTesterProduct;