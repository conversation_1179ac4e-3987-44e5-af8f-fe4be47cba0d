import React from 'react';
import PageLayout from '../../components/layout/PageLayout';

const leadership = [
  {
    title: 'Managing Director',
    name: '<PERSON><PERSON>',
    photo: '/path/to/md-photo.jpg',
    description: 'Strategic visionary driving organizational excellence and sustainable growth through innovative leadership and market expertise.',
    highlights: ['20+ Years Industry Experience', 'Strategic Growth Leader', 'Innovation Champion']
  },
  {
    title: 'Director',
    name: '<PERSON>',
    photo: '/path/to/director-photo.jpg',
    description: 'Operational excellence expert focused on driving efficiency, team development, and delivering exceptional business results.',
    highlights: ['Operational Excellence', 'Team Development', 'Business Strategy']
  }
];

const OurLeadership: React.FC = () => {
  return (
    <PageLayout category="about" hideHero={true} hideBreadcrumbs={true}>
      <div style={{ 
        maxWidth: '1000px', 
        margin: '0 auto', 
        padding: '4rem 1rem',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        minHeight: '100vh'
      }}>
        {/* Header Section */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          <h1 style={{ 
            color: '#1a202c', 
            fontSize: '3.0rem', 
            fontWeight: 800, 
            marginBottom: '1rem', 
            letterSpacing: '-2px',
            background: 'linear-gradient(135deg, #1a202c 0%, #4a5568 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            Driving Innovation Through Leadership
          </h1>
          <div style={{
            width: '80px',
            height: '4px',
            background: 'linear-gradient(90deg, #3182ce, #63b3ed)',
            margin: '0 auto 1.5rem auto',
            borderRadius: '2px'
          }}></div>
          <p style={{ 
            color: '#4a5568', 
            fontSize: '1.2rem', 
            maxWidth: '650px', 
            margin: '0 auto',
            lineHeight: 1.7,
            fontWeight: 400
          }}>
            Meet the visionary leaders driving our organization forward with expertise, 
            innovation, and unwavering commitment to excellence.
          </p>
        </div>

        {/* Leadership Cards */}
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: '3rem', 
          justifyContent: 'center',
          alignItems: 'stretch'
        }}>
          {leadership.map((leader, idx) => (
            <div
              key={idx}
              style={{
                background: 'linear-gradient(135deg, #ffffff 0%, #f7fafc 100%)',
                border: '1px solid #e2e8f0',
                borderRadius: '24px',
                padding: '3rem 2rem',
                width: '400px',
                minHeight: '450px',
                boxShadow: '0 20px 60px rgba(0,0,0,0.08), 0 8px 25px rgba(0,0,0,0.06)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                cursor: 'pointer',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseOver={e => {
                e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';
                e.currentTarget.style.boxShadow = '0 30px 80px rgba(0,0,0,0.15), 0 12px 40px rgba(0,0,0,0.1)';
              }}
              onMouseOut={e => {
                e.currentTarget.style.transform = 'none';
                e.currentTarget.style.boxShadow = '0 20px 60px rgba(0,0,0,0.08), 0 8px 25px rgba(0,0,0,0.06)';
              }}
            >
              {/* Decorative Background Element */}
              <div style={{
                position: 'absolute',
                top: '-50px',
                right: '-50px',
                width: '150px',
                height: '150px',
                background: 'linear-gradient(135deg, #3182ce20, #63b3ed20)',
                borderRadius: '50%',
                zIndex: 0
              }}></div>

              {/* Profile Image */}
              <div style={{
                width: '140px',
                height: '140px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #3182ce, #63b3ed)',
                marginBottom: '2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                position: 'relative',
                zIndex: 1,
                border: '4px solid #ffffff',
                boxShadow: '0 8px 32px rgba(49, 130, 206, 0.3)'
              }}>
                <img
                  src={leader.photo}
                  alt={leader.name}
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'cover', 
                    borderRadius: '50%'
                  }}
                  onError={e => (e.currentTarget.style.display = 'none')}
                />
                {/* Initials fallback */}
                <span style={{ 
                  color: '#ffffff', 
                  fontSize: '3rem', 
                  fontWeight: 700,
                  position: 'absolute',
                  zIndex: 2
                }}>
                  {leader.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>

              {/* Content */}
              <div style={{ textAlign: 'center', zIndex: 1, flex: 1, display: 'flex', flexDirection: 'column' }}>
                <h2 style={{ 
                  color: '#1a202c', 
                  fontSize: '1.5rem', 
                  fontWeight: 700, 
                  margin: '0 0 0.5rem 0', 
                  letterSpacing: '-0.5px'
                }}>
                  {leader.name}
                </h2>
                
                <div style={{
                  background: 'linear-gradient(90deg, #3182ce, #63b3ed)',
                  color: '#ffffff',
                  fontSize: '0.9rem',
                  fontWeight: 600,
                  padding: '0.5rem 1.5rem',
                  borderRadius: '20px',
                  margin: '0 0 1.5rem 0',
                  letterSpacing: '0.5px',
                  textTransform: 'uppercase'
                }}>
                  {leader.title}
                </div>

                <p style={{ 
                  color: '#4a5568', 
                  fontSize: '1rem', 
                  lineHeight: 1.6, 
                  marginBottom: '2rem',
                  flex: 1
                }}>
                  {leader.description}
                </p>

                {/* Highlights */}
                <div style={{ 
                  display: 'flex', 
                  flexWrap: 'wrap', 
                  gap: '0.5rem', 
                  justifyContent: 'center',
                  marginTop: 'auto'
                }}>
                  {leader.highlights.map((highlight, hidx) => (
                    <span
                      key={hidx}
                      style={{
                        background: '#f7fafc',
                        color: '#2d3748',
                        fontSize: '0.8rem',
                        fontWeight: 600,
                        padding: '0.4rem 1rem',
                        borderRadius: '15px',
                        border: '1px solid #e2e8f0'
                      }}
                    >
                      {highlight}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div style={{
          textAlign: 'center',
          marginTop: '4rem',
          padding: '2rem',
          background: 'rgba(255, 255, 255, 0.8)',
          borderRadius: '16px',
          border: '1px solid #e2e8f0'
        }}>
          <p style={{
            color: '#4a5568',
            fontSize: '1.1rem',
            fontStyle: 'italic',
            margin: 0
          }}>
            "Leadership is not about being in charge. It's about taking care of those in your charge."
          </p>
        </div>
      </div>
    </PageLayout>
  );
};

export default OurLeadership;