import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Mail,
  Phone,
  Shield,
  Gauge,
  FileText,
  ChevronDown,
  Database,
  Zap,
  ChevronLeft,
  ChevronRight,
  Monitor,
  Wifi,
  Battery,
  Thermometer,
  BarChart
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

// --- Insulation Testers Data ---
export const insulationTesters = {
  1: {
    id: 1,
    model: 'CA 6522/CA 6528',
    title: '1kV Insulation Testers',
    subtitle: 'Professional 1kV Insulation Testers',
    image: '/insulation testers/CA 6522.png',
    images: [
      '/insulation testers/CA 6522.png',
      '/insulation testers/CA 6528.png'
    ],
    voltage: 'CAT III 600V',
    measurement: '1kV Testing',
    accuracy: '50 kΩ to 40 GΩ',
    price: 'Contact for pricing',
    description: 'Professional 1kV insulation testers designed for electrical maintenance and troubleshooting with visual alarms and high accuracy measurements.',
    features: [
      'Display : 4000 counts, double digital backlit LCD screen',
      'Logarithmic bargraph (CA 6522)',
      'Continuity at 200mA',
      'CA 6528 : 0.02Ω - 40Ω CA 6522 : 0.00Ω - 10.00Ω',
      'Visual alarm : Blue/Red backlighting (CA 6528)',
      'Timer (mins) : Upto 39:59',
      'Automatic shutdown, Hold,Manual / Lock / Duration modes',
      'IP 40 (CA 6528) , IP 54 (CA 6522)'
    ],
    technicalSpecs: {
      'Test Voltage': '250 V, 500 V, 1000 V',
      'Range/Accuracy': 'CA 6522 : 50 kΩ to 40 GΩ /± (3% +2 counts), CA 6528 : 50 kΩ to 11GΩ / ± (1.5% R+10 pt)',
      'Voltage Range': 'Upto 700 V',
      'Display': '4000 counts, double digital backlit LCD screen',
      'Continuity': '200mA test current',
      'Timer': 'Up to 39:59 minutes',
      'Protection Rating': 'IP 40 (CA 6528), IP 54 (CA 6522)',
      'Power Supply': 'Battery operated with auto shutdown'
    },
    applications: [
      'Electrical maintenance',
      'Troubleshooting',
      'Industrial field use',
      'Equipment testing',
      'Safety inspections'
    ],
    advantages: [
      'High accuracy',
      'Visual alarms',
      'IP rated for durability',
      'Long battery life',
      'Dual model options'
    ]
  },
  2: {
    id: 2,
    model: 'CA 6524/CA 6526',
    title: '1kV Insulation Testers',
    subtitle: 'Advanced 1kV Insulation Testers',
    image: '/insulation testers/CA 6524.png',
    images: [
      '/insulation testers/CA 6524.png',
      '/insulation testers/CA 6526.png'
    ],
    voltage: 'CAT III 700V',
    measurement: '1kV Testing',
    accuracy: '10 kΩ to 200 GΩ',
    price: 'Contact for pricing',
    description: 'Advanced 1kV insulation testers with Bluetooth connectivity, large memory storage, and configurable alarms for professional testing applications.',
    features: [
      'Display : 4000 counts, double + bargraph',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'PI & DAR',
      'Visual Pass/Fail : Red/Green (CA 6526)',
      'Timer (mins) : Upto 39:59',
      'Configurable alarms',
      'Memory : 300 measurements (CA 6524), 1300 measurements (CA 6526)',
      'Communication : Bluetooth (CA 6526)'
    ],
    technicalSpecs: {
      'Test Voltage': '50 V, 100 V, 250 V, 500 V, 1000 V',
      'Test current': '0.01μA to 2mA',
      'Range': '10 kΩ to 200 GΩ',
      'Accuracy': '± (3% +2 counts)',
      'Voltage Range': 'Upto 700 V',
      'Capacitance': '0.1 nF to 10 μF (CA 6526)',
      'Memory': '300 measurements (CA 6524), 1300 measurements (CA 6526)',
      'Communication': 'Bluetooth (CA 6526)',
      'PI & DAR': 'Polarization Index and Dielectric Absorption Ratio calculations',
      'Timer': 'Up to 39:59 minutes'
    },
    applications: [
      'Industrial testing',
      'Field testing',
      'Quality assurance',
      'Preventive maintenance',
      'R&D applications'
    ],
    advantages: [
      'Bluetooth connectivity',
      'Large memory',
      'Configurable alarms',
      'PI & DAR calculations',
      'Multiple test voltages'
    ]
  },
  3: {
    id: 3,
    model: 'CA 6532/CA 6534/CA 6536',
    title: 'Insulation Testers Special Models',
    subtitle: 'Specialized Insulation Testers',
    image: '/insulation testers/CA 6532.png',
    images: [
      '/insulation testers/CA 6532.png',
      '/insulation testers/CA 6534.png',
      '/insulation testers/CA 6536.png'
    ],
    voltage: 'CAT III 700V',
    measurement: 'Specialized Testing',
    accuracy: '2 kΩ to 50 GΩ',
    price: 'Contact for pricing',
    description: 'Specialized insulation testers designed for telecommunications, electronics, and aerospace applications with customizable voltage steps and advanced features.',
    features: [
      'Display : 4000 counts, double + bargraph',
      'PI & DAR (CA 6532)',
      'Continuity at 200mA (0.00Ω - 10.00Ω)/ 20mA (0.0Ω - 100.0Ω)',
      'Timer (mins) : Upto 39:59',
      'Memory : 1300 records (CA 6532 & 6534)',
      'Communication : Bluetooth (CA 6532 & 6534)'
    ],
    technicalSpecs: {
      'Test Voltage/Insulation Range': 'CA 6532 : 50 V, 100 V/10 kΩ to 20 GΩ; CA 6534 : 10 V, 25 V, 100 V, 250 V, 500 V/ 2 kΩ to 50 GΩ; CA 6536 : 10 V to 100 V in 1V step/2 kΩ to 20 GΩ',
      'Test current': '0.01μA to 2mA',
      'Accuracy': '± (3% +2 counts)',
      'Voltage Range': 'Upto 700 V',
      'Capacitance': '0.1 nF to 10 nF (CA 6532)',
      'Memory': '1300 records (CA 6532 & 6534)',
      'Communication': 'Bluetooth (CA 6532 & 6534)',
      'Variable Voltage': '1V step increments (CA 6536)',
      'Specialized Ranges': 'Low voltage testing capability'
    },
    applications: [
      'Telecommunications',
      'Electronics',
      'Avionics, Space & Defense',
      'Low voltage systems',
      'Sensitive equipment testing'
    ],
    advantages: [
      'Specialized voltage steps',
      'Bluetooth connectivity',
      'Large memory',
      'Variable voltage control',
      'Industry-specific design'
    ]
  },
  4: {
    id: 4,
    model: 'CA 6505/CA 6545',
    title: '5kV Insulation Testers',
    subtitle: '5kV High Voltage Insulation Testers',
    image: '/insulation testers/CA 6505.png',
    images: [
      '/insulation testers/CA 6505.png',
      '/insulation testers/CA 6545.png'
    ],
    voltage: 'CAT III 5100V',
    measurement: '5kV Testing',
    accuracy: '10 kΩ to 10 TΩ',
    price: 'Contact for pricing',
    description: 'High voltage 5kV insulation testers for power utilities and industrial plants with programmable test duration and advanced safety features.',
    features: [
      'Display : Backlit LCD graphic display with bargraph',
      'Programmable test duration',
      'Automatic calculation of the DAR/PI',
      'DD calculation (CA 6545)',
      'Locking test voltage',
      'Programmable Alarms (CA 6545)',
      'Smoothing of Display (CA 6545)',
      'Automatic detection of the presence of AC or DC external voltage on terminals',
      'Auto power save mode to save battery power'
    ],
    technicalSpecs: {
      'Test voltage (Fixed/Adjustable)': '500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
      'Range': '10 kΩ to 10 TΩ',
      'Accuracy': '± 5% +3 pts',
      'Voltage Range': 'Upto 5100 V',
      'Leakage Current': 'Upto 3mA',
      'Capacitance': 'Upto 49.99 μF',
      'DAR/PI Calculation': 'Automatic calculation',
      'DD Calculation': 'Dielectric Discharge (CA 6545)',
      'Safety Features': 'Automatic voltage detection',
      'Power Management': 'Auto power save mode'
    },
    applications: [
      'High voltage equipment',
      'Power utilities',
      'Industrial plants',
      'Transformer testing',
      'Cable testing'
    ],
    advantages: [
      'Programmable alarms',
      'Auto power save',
      'High voltage range',
      'Safety voltage detection',
      'Advanced calculations'
    ]
  },
  5: {
    id: 5,
    model: 'CA 6547/CA 6549',
    title: '5kV Insulation Testers',
    subtitle: 'Advanced 5kV Insulation Testers',
    image: '/insulation testers/CA 6547.png',
    images: [
      '/insulation testers/CA 6547.png',
      '/insulation testers/CA 6549.png'
    ],
    voltage: 'CAT III 5100V',
    measurement: '5kV Testing',
    accuracy: '10 kΩ to 10 TΩ',
    price: 'Contact for pricing',
    description: 'Advanced 5kV insulation testers with USB connectivity, large memory storage, and direct R(t) curve plotting for comprehensive power system analysis.',
    features: [
      'Large backlit LCD screen, with digital display & bargraph',
      'Automatic calculation of the DAR/PI/DD ratios',
      'Programmable alarms',
      'Displays a error code in an anamoly condition (CA 6549)',
      'Direct plotting resistance over time (R(t)) curves in display (CA 6549)',
      'Calculation of R at Reference Temperature (T°) (CA 6549)',
      'Memory : 128KB storage capacity',
      'Communication : USB (Two-Way)',
      'PC interface'
    ],
    technicalSpecs: {
      'Test Voltage (Fixed/Adjustable)': '500 V, 1000 V, 2500 V, 5000 V/40 V to 5100 V in 10 V or 100 V increments',
      'Range': '10 kΩ to 10 TΩ',
      'Accuracy': '±5% +3 pts',
      'Voltage Range': 'Upto 5100 V',
      'Leakage Current': 'Upto 3mA',
      'Capacitance': 'Upto 49.99 μF',
      'Memory': '128KB storage capacity',
      'Communication': 'USB (Two-Way)',
      'R(t) Plotting': 'Direct resistance over time curves (CA 6549)',
      'Temperature Compensation': 'R at Reference Temperature calculation (CA 6549)',
      'Error Detection': 'Anomaly condition error codes (CA 6549)',
      'PC Interface': 'Full computer connectivity'
    },
    applications: [
      'Power system analysis',
      'Industrial maintenance',
      'Utility testing',
      'Research applications',
      'Advanced diagnostics'
    ],
    advantages: [
      'USB communication',
      'Advanced memory',
      'Direct plotting of R(t)',
      'Temperature compensation',
      'PC interface capability'
    ]
  },
  6: {
    id: 6,
    model: 'CA 6550/CA 6555',
    title: '10kV/15kV Insulation Testers',
    subtitle: 'Ultra High Voltage Insulation Testers',
    image: '/insulation testers/CA 6555.png',
    images: [
      '/insulation testers/CA 6555.png',
      '/insulation testers/CA 6555-2.png',
      '/insulation testers/CA 6555-3.png',
      '/insulation testers/CA 6550.png'
    ],
    voltage: 'CAT III 15000V',
    measurement: '10kV/15kV Testing',
    accuracy: '10 kΩ to 30 TΩ',
    price: 'Contact for pricing',
    description: 'Ultra high voltage insulation testers for utility and grid monitoring with advanced voltage ramp capabilities and comprehensive data logging.',
    features: [
      'Large graphical LCD display with backlight & bargraph',
      'Calculation of the DAR/PI/DD ratios',
      'Programmable test duration',
      'Timer (mins) : Upto 99:59',
      'Voltage ramp & step with " burn - in", "early break" & "I - limit" modes',
      'Memory : 80,000 points',
      'Communication :USB',
      'PC interface'
    ],
    technicalSpecs: {
      'Test Voltage/Insulation range': 'CA 6550 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V/10 kΩ to 25 TΩ; CA 6555 : 500 V, 1000 V, 2500 V, 5000 V, 10,000 V, 15,000 V/10 kΩ to 30 TΩ',
      'Accuracy': '±5% +3 pts',
      'Voltage Range': 'Upto 2500 V AC/Upto 4000 V DC',
      'Leakage Current': 'Upto 8mA',
      'Capacitance': 'Upto 19.99 μF',
      'Memory': '80,000 points',
      'Communication': 'USB interface',
      'Timer': 'Up to 99:59 minutes',
      'Voltage Modes': 'Ramp, step, burn-in, early break, I-limit',
      'Maximum Test Voltage': 'Up to 15,000V (CA 6555)',
      'Data Points': 'Comprehensive data logging capability'
    },
    applications: [
      'Utility and grid monitoring',
      'Research and development',
      'Critical facility testing',
      'High voltage cable testing',
      'Power generation equipment'
    ],
    advantages: [
      'Ultra high voltage',
      'Large memory',
      'USB interface',
      'Advanced voltage modes',
      'Comprehensive data logging'
    ]
  }
};

const PDF_URL = '/T&M April 2025.pdf';

const InsulationTesterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);
  
  const id = Number(productId);
  const product = insulationTesters[id as keyof typeof insulationTesters];
  const productList = Object.values(insulationTesters);

  useEffect(() => {
    if (!product) {
      navigate('/measure/insulation-testers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Insulation Tester`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('logging')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('communication') || feature.toLowerCase().includes('bluetooth') || feature.toLowerCase().includes('usb')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('test')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('timer') || feature.toLowerCase().includes('alarm') || feature.toLowerCase().includes('programmable')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('ip') || feature.toLowerCase().includes('protection')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('calculation') || feature.toLowerCase().includes('dar') || feature.toLowerCase().includes('pi') || feature.toLowerCase().includes('plotting')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-black mb-2">
                Insulation Testers
              </h1>
              <p className="text-xl text-black font-medium">
                Professional Insulation Testing Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => navigate(`/measure/insulation-testers/product/${prod.id}`)}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/insulation-testers#products-section')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Safety Rating</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open(PDF_URL, '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{ 
                        maxHeight: '200px',
                        maxWidth: '200px',
                        background: 'transparent',
                        mixBlendMode: 'multiply',
                        filter: 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.features.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.features.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.features.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.features.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.features.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on insulation testing solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Mail className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default InsulationTesterProduct;