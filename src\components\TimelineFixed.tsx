import React, { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';
import ThermalVisionModel from './3d/ThermalVisionModel';

// 3D Stabilizer Scene Component (simplified without title)
const Stabilizer3DScene = ({ className }) => {
  return (
    <div className={`w-full h-full flex flex-col items-center justify-center ${className}`}>
      {/* 3D Model Canvas */}
      <div className="w-full flex justify-center items-center my-4" style={{height: '350px', maxWidth: '400px'}}>
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          shadows
          gl={{ preserveDrawingBuffer: true }}
        >
          <ambientLight intensity={0.5} />
          <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
          <pointLight position={[-10, -10, -10]} intensity={0.5} />
          <ThermalVisionModel />
          <OrbitControls enableZoom={false} enablePan={false} />
          <Environment preset="city" />
        </Canvas>
      </div>
      {/* Bottom content about transformation */}
      <div className="space-y-6 mt-2 w-full">
        <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
          From analog beginnings to digital precision, our stabilizers have evolved to meet the changing needs of modern industry, ensuring consistent power and equipment protection.
        </h4>
      </div>
    </div>
  );
};

const TimelineFixed = () => {
  return (
    <section id="timeline" className="relative py-16 sm:py-20 lg:py-24 overflow-hidden font-['Open_Sans'] bg-white">
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-stretch h-full min-h-[500px]">
          {/* Content Section - Left Side */}
          <div className="text-left flex flex-col justify-start h-full min-h-[500px] max-w-2xl mx-auto">
            {/* Enhanced Header */}
            <div className="mb-2 sm:mb-4">
              <span className="inline-block px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-lg sm:text-xl font-bold text-gray-800 uppercase tracking-wider font-['Open_Sans'] mb-4">
                Our Story
              </span>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-4xl font-bold text-gray-900 font-['Open_Sans'] leading-tight">
                40+ Years of Power Excellence
              </h1>
            </div>
            {/* New Content */}
            <div className="space-y-6">
            <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                More than four decades ago, a handful of engineers huddled over humming transformers and half‑scribbled sketches, convinced that if power could be tamed, industry could dream bigger. That spark became KRYKARD.
              </h4>
              <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                At KRYKARD, we've spent over 40 years helping power flow better—cleaner, steadier, and smarter—for industries across India.
              </h4>
              <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                With over 5,00,000 Power Conditioners and 1,50,000 Load Managers installed, our journey is built on solving real problems, one voltage fluctuation at a time.
              </h4>
              <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                From factory floors to control rooms, our 500+ strong team and nationwide service network ensure our customers aren't just powered—but empowered.
              </h4>
              <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                We're not just about machines. We're about making power work for people. That's the Atandra way.
              </h4>
            </div>
          </div>

          {/* 3D Model Section - Right Side */}
          <div className="flex flex-col justify-start h-full min-h-[500px] max-w-2xl mx-auto">
            {/* 3D Model at Top - Larger Size */}
            <div className="w-full flex justify-center items-center mb-6" style={{minHeight: '500px', maxHeight: '900px', maxWidth: '1000px'}}>
              <ThermalVisionModel className="w-full h-full" />
            </div>
            {/* Innovation Badge Only (no title) and content below model */}
            <div className="flex flex-col space-y-6 w-full">
              <span className="inline-block px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-lg sm:text-xl font-bold text-gray-800 uppercase tracking-wider font-['Open_Sans']">
                Innovation
              </span>
              <h4 className="text-lg sm:text-xl lg:text-2xl text-gray-800 leading-relaxed font-['Open_Sans'] text-justify max-w-3xl">
                "Our latest Stabilizer after 4 decades of evolution"
              </h4>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TimelineFixed;