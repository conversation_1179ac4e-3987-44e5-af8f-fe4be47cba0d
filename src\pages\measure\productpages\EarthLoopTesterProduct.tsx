import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const EarthLoopTesterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'CA-6417', model: 'CA 6417', subtitle: 'Clamp-On Earth Loop Tester' },
    { id: 'CA-6418', model: 'CA 6418', subtitle: 'Advanced Clamp-On Earth Tester' }
  ];

  // Complete product data
  const productData = {
    'CA-6417': {
      id: 'CA-6417',
      model: 'CA 6417',
      subtitle: 'Clamp-On Earth Loop Tester',
      image: '/earth_loop_testers/CA 6417.png',
      images: [
        '/earth_loop_testers/CA 6417.png',
        '/earth_loop_testers/CA 6418.png',
        '/earth_loop_testers/images-removebg-preview (1).png'
      ],
      voltage: 'CAT III 300V',
      measurement: 'Earth Loop Testing',
      accuracy: '0.01Ω to 1200Ω',
      price: 'Contact for pricing',
      description: 'Non-invasive clamp-on tester for fast, safe earth resistance measurement without disconnecting the ground system. Advanced technology for reliable testing.',
      keyFeatures: [
        'Clamp-on measurement for non-invasive testing',
        'Automatic self-calibration',
        'Large LCD display with backlight',
        'Measures earth resistance and leakage current',
        'Memory for up to 300 measurements',
        'Alarm function for threshold monitoring',
        'Robust, portable design',
        'Data hold and min/max functions',
        'Auto power-off feature',
        'Weather-resistant construction'
      ],
      technicalSpecs: {
        'Model': 'CA 6417 Clamp-On Earth Loop Tester',
        'Measurement Range': '0.01Ω to 1200Ω',
        'Current Measurement': '0.2mA to 40A',
        'Display': 'LCD, 4000 counts with backlight',
        'Jaw Opening': '35mm diameter',
        'Memory Storage': '300 measurements with date/time',
        'Power Supply': '4 x 1.5V AA batteries',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Humidity': '85% RH max',
        'Safety Rating': 'CAT III 300V',
        'Weight': '1.1kg',
        'Dimensions': '260 x 105 x 55mm',
        'Auto Power Off': 'Configurable (15 min default)',
        'Data Interface': 'USB for PC communication'
      },
      applications: [
        'Ground system verification',
        'Industrial plant maintenance',
        'Lightning protection system testing',
        'Utility and substation testing',
        'Electrical installation verification',
        'Equipment grounding assessment'
      ],
      advantages: [
        'Non-invasive clamp-on measurement',
        'No disconnection of ground conductors required',
        'Fast and safe testing procedure',
        'High accuracy and repeatability',
        'Portable and rugged design',
        'Comprehensive data logging capabilities'
      ]
    },
    'CA-6418': {
      id: 'CA-6418',
      model: 'CA 6418',
      subtitle: 'Advanced Clamp-On Earth Tester',
      image: '/earth_loop_testers/CA 6418.png',
      images: [
        '/earth_loop_testers/CA 6418.png',
        '/earth_loop_testers/CA 6417.png',
        '/earth_loop_testers/images-removebg-preview (1).png'
      ],
      voltage: 'CAT III 300V',
      measurement: 'Advanced Earth Testing',
      accuracy: '0.01Ω to 2000Ω',
      price: 'Contact for pricing',
      description: 'Advanced clamp-on earth tester with Bluetooth communication and extended measurement range for professional applications requiring enhanced connectivity.',
      keyFeatures: [
        'Bluetooth communication for data transfer',
        'Extended measurement range up to 2000Ω',
        'Automatic self-calibration',
        'Large LCD display with advanced features',
        'Measures earth resistance and leakage current',
        'Enhanced alarm and memory functions',
        'Rugged, ergonomic design',
        'PC software compatibility',
        'Real-time data monitoring',
        'Professional reporting capabilities'
      ],
      technicalSpecs: {
        'Model': 'CA 6418 Advanced Clamp-On Earth Tester',
        'Measurement Range': '0.01Ω to 2000Ω',
        'Current Measurement': '0.2mA to 40A',
        'Display': 'LCD, 4000 counts with backlight',
        'Jaw Opening': '35mm diameter',
        'Memory Storage': '2000 measurements with date/time',
        'Bluetooth': 'Yes, Bluetooth 4.0',
        'Power Supply': '4 x 1.5V AA batteries',
        'Operating Temperature': '-10°C to +55°C',
        'Storage Temperature': '-20°C to +65°C',
        'Humidity': '85% RH max',
        'Safety Rating': 'CAT III 300V',
        'Weight': '1.2kg',
        'Dimensions': '260 x 105 x 55mm',
        'PC Software': 'Included analysis software',
        'Data Interface': 'Bluetooth + USB'
      },
      applications: [
        'Ground system verification',
        'Industrial plant maintenance',
        'Lightning protection system testing',
        'Utility and substation testing',
        'Electrical installation verification',
        'Equipment grounding assessment',
        'Power quality analysis'
      ],
      advantages: [
        'Wireless Bluetooth data transfer',
        'Extended measurement range capability',
        'Professional PC software included',
        'Advanced data logging and analysis',
        'Enhanced connectivity options',
        'Comprehensive reporting features'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/earth-loop-testers');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Earth Loop Tester`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('tft') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('data') || feature.toLowerCase().includes('logging')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('connectivity') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('bluetooth')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power') && !feature.toLowerCase().includes('measurement')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal') || feature.toLowerCase().includes('weather')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('clamp')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('bandwidth') || feature.toLowerCase().includes('sample') || feature.toLowerCase().includes('range') || feature.toLowerCase().includes('calibration')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('cat') || feature.toLowerCase().includes('isolated') || feature.toLowerCase().includes('alarm') || feature.toLowerCase().includes('protection')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('analysis') || feature.toLowerCase().includes('monitoring') || feature.toLowerCase().includes('reporting') || feature.toLowerCase().includes('software')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Earth Loop Testers
              </h1>
              <p className="typography-h4 text-black">
                Professional Earth Loop Testing Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => navigate(`/measure/earth-loop-testers/product/${prod.id}`)}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/earth-loop-testers')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Safety Rating</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{ 
                        maxHeight: '200px',
                        maxWidth: '200px',
                        background: 'transparent',
                        mixBlendMode: 'multiply',
                        filter: 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on earth loop testing solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default EarthLoopTesterProduct;