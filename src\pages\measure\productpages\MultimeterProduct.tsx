import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart,
  ChevronRight
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const MultimeterProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [featuresExpanded, setFeaturesExpanded] = useState(false);
  const [specsExpanded, setSpecsExpanded] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'mtx203', model: 'MTX 203', subtitle: 'Basic Digital Multimeter' },
    { id: 'dmm210', model: 'DMM 210', subtitle: 'Standard Digital Multimeter' },
    { id: 'dmm220', model: 'DMM 220', subtitle: 'Enhanced Standard Digital Multimeter' },
    { id: 'dmm230', model: 'DMM 230', subtitle: 'TRMS Standard Digital Multimeter' },
    { id: 'dmm240', model: 'DMM 240', subtitle: 'Advanced Digital Multimeter' },
    { id: 'ca5273', model: 'CA 5273', subtitle: 'CA Advanced Series Multimeter' },
    { id: 'mtx3291', model: 'MTX 3291', subtitle: 'High Resolution Multimeter' },
    { id: 'ca5292', model: 'CA 5292', subtitle: 'Professional Digital Multimeter' },
    { id: 'f65', model: 'F65', subtitle: 'RMS Leakage Current Clamp' }
  ];

  // Complete product data with multiple images support
  const productData = {
    mtx203: {
      id: 'mtx203',
      model: 'MTX 203',
      subtitle: 'Basic Digital Multimeter',
      image: '/multimeter/MTX 203.png',
      images: [
        '/multimeter/MTX 203.png',
        '/multimeter/MTX 203.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '6000 counts LCD',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The MTX 203 provides essential electrical measurement capabilities in a compact, user-friendly package. Perfect for basic electrical testing and troubleshooting with reliable performance.',
      keyFeatures: [
        'Display: 6000 counts monochrome digital display with blue backlighting',
        'Audible continuity & Diode test',
        'VLowZ, HOLD, NCV',
        'Min, Max values',
        'IP 54 protection rating',
        'Auto power-off function',
        'Portable and rugged construction',
        'Easy-to-use interface',
        'Voltage measurement up to 1000V',
        'Current measurement capability',
        'Resistance measurement range',
        'Temperature measurement function'
      ],
      technicalSpecs: {
        'Voltage Range': 'V (AC): 0.4 V to 600 V & 10 mV to 1000 V, V (AC/DC/AC+DC): 10 mV to 1000 V',
        'Current Range': 'A (AC): 2 mA to 10 A & 10 μA to 10 A',
        'Resistance': 'Range: Up to 40 MΩ',
        'Capacitance': 'Range: Up to 100 mF',
        'Temperature': 'Range: Up to 1,200°C',
        'Display': '6000 counts monochrome LCD with blue backlighting',
        'Power Supply': 'Battery powered',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 54',
        'Dimensions': '180 x 90 x 50 mm',
        'Weight': '0.4 kg',
        'Accuracy': '±0.5% reading',
        'Display Update Rate': '3 times per second',
        'Battery Life': 'Up to 200 hours continuous use'
      },
      applications: [
        'Basic electrical testing and troubleshooting',
        'Residential electrical work',
        'Educational and training purposes',
        'Field service and maintenance',
        'Electronics repair and diagnostics',
        'Automotive electrical testing'
      ],
      advantages: [
        'Compact and portable design',
        'Easy-to-read display',
        'Essential measurement functions',
        'Reliable performance',
        'Cost-effective solution',
        'User-friendly operation'
      ]
    },
    dmm210: {
      id: 'dmm210',
      model: 'DMM 210',
      subtitle: 'Standard Digital Multimeter',
      image: '/multimeter/DMM 210.png',
      images: [
        '/multimeter/DMM 210.png',
        '/multimeter/DMM 210.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '6,000 counts LCD',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The DMM 210 provides reliable electrical measurements with enhanced features including bargraph display and autorange functionality for professional electrical testing applications.',
      keyFeatures: [
        'Display: 6,000 counts backlit with bargraph',
        'Measurement Type: Average',
        'Calibre Selection: Autorange/Manual',
        'Audible continuity & Diode test',
        'Automatic shutdown (deactivatable)',
        'Relative mode – MIN, MAX',
        'IP 67 protection rating',
        'Professional-grade construction',
        'Enhanced voltage measurement',
        'Improved current sensing',
        'Advanced frequency measurement',
        'Temperature compensation'
      ],
      technicalSpecs: {
        'Voltage Range': 'V(AC): 6 V to 1000 V, V(DC): 600 mV to 1000 V',
        'AC Bandwidth': 'Up to 1 kHz',
        'Current Range': 'A (AC/DC): 600 μA to 10 A',
        'Resistance': '600 Ω to 60 MΩ',
        'Frequency': 'Up to 10 MHz',
        'Display': '6,000 counts backlit with bargraph',
        'Measurement Type': 'Average',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '190 x 95 x 55 mm',
        'Weight': '0.5 kg',
        'Accuracy (VDC)': '±(0.5% + 1 digit)',
        'Accuracy (VAC)': '±(1.0% + 3 digits)',
        'Input Impedance': '10 MΩ',
        'Max Input Voltage': '1000V CAT III'
      },
      applications: [
        'Professional electrical testing',
        'Industrial maintenance',
        'Electrical installation verification',
        'Power system diagnostics',
        'Equipment troubleshooting',
        'Quality control testing'
      ],
      advantages: [
        'Autorange functionality',
        'Bargraph display',
        'IP 67 protection',
        'Professional accuracy',
        'Reliable performance',
        'Versatile measurement capabilities'
      ]
    },
    dmm220: {
      id: 'dmm220',
      model: 'DMM 220',
      subtitle: 'Enhanced Standard Digital Multimeter',
      image: '/multimeter/DMM 210.png',
      images: [
        '/multimeter/DMM 210.png',
        '/multimeter/DMM 210.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '6,000 counts LCD',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The DMM 220 builds upon the DMM 210 with additional measurement capabilities including capacitance and temperature measurement for comprehensive electrical testing.',
      keyFeatures: [
        'Display: 6,000 counts backlit with bargraph',
        'Measurement Type: Average',
        'Calibre Selection: Autorange/Manual',
        'Audible continuity & Diode test',
        'Automatic shutdown (deactivatable)',
        'Relative mode – MIN, MAX',
        'Capacitance measurement up to 1000 μF',
        'Temperature measurement up to 750°C',
        'IP 67 protection rating',
        'Enhanced measurement accuracy',
        'Improved display visibility',
        'Advanced safety features'
      ],
      technicalSpecs: {
        'Voltage Range': 'V(AC): 6 V to 1000 V, V(DC): 600 mV to 1000 V',
        'AC Bandwidth': 'Up to 1 kHz',
        'Current Range': 'A (AC/DC): 600 μA to 10 A',
        'Resistance': '600 Ω to 60 MΩ',
        'Frequency': 'Up to 10 MHz',
        'Capacitance': 'Up to 1000 μF',
        'Temperature': 'Up to 750°C',
        'Display': '6,000 counts backlit with bargraph',
        'Measurement Type': 'Average',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '190 x 95 x 55 mm',
        'Weight': '0.5 kg',
        'Safety Rating': 'CAT III 1000V, CAT IV 600V',
        'Input Protection': 'Fused inputs for safety'
      },
      applications: [
        'Professional electrical testing',
        'HVAC system diagnostics',
        'Industrial maintenance',
        'Electrical installation verification',
        'Temperature monitoring',
        'Capacitor testing'
      ],
      advantages: [
        'Enhanced measurement capabilities',
        'Temperature measurement',
        'Capacitance testing',
        'Professional accuracy',
        'IP 67 protection',
        'Comprehensive functionality'
      ]
    },
    dmm230: {
      id: 'dmm230',
      model: 'DMM 230',
      subtitle: 'TRMS Standard Digital Multimeter',
      image: '/multimeter/DMM 210.png',
      images: [
        '/multimeter/DMM 210.png',
        '/multimeter/DMM 210.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '6,000 counts LCD',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The DMM 230 features True RMS measurement capability for accurate readings of non-sinusoidal waveforms, making it ideal for modern electrical systems with electronic loads.',
      keyFeatures: [
        'Display: 6,000 counts backlit with bargraph',
        'Measurement Type: TRMS (True RMS)',
        'Calibre Selection: Autorange/Manual',
        'Audible continuity & Diode test',
        'Automatic shutdown (deactivatable)',
        'Relative mode – MIN, MAX',
        'Capacitance measurement up to 1000 μF',
        'Temperature measurement up to 750°C',
        'IP 67 protection rating',
        'True RMS for accurate AC measurements',
        'Crest factor up to 3:1',
        'Advanced waveform analysis'
      ],
      technicalSpecs: {
        'Voltage Range': 'V(AC): 6 V to 1000 V, V(DC): 600 mV to 1000 V',
        'AC Bandwidth': 'Up to 1 kHz',
        'Current Range': 'A (AC/DC): 600 μA to 10 A',
        'Resistance': '600 Ω to 60 MΩ',
        'Frequency': 'Up to 10 MHz',
        'Capacitance': 'Up to 1000 μF',
        'Temperature': 'Up to 750°C',
        'Display': '6,000 counts backlit with bargraph',
        'Measurement Type': 'TRMS (True RMS)',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '190 x 95 x 55 mm',
        'Weight': '0.5 kg',
        'Crest Factor': 'Up to 3:1 at full scale',
        'TRMS Accuracy': '±(1.0% + 3 digits) 45-65 Hz'
      },
      applications: [
        'TRMS measurement applications',
        'Electronic load testing',
        'Variable frequency drive diagnostics',
        'Power quality assessment',
        'Industrial maintenance',
        'HVAC system analysis'
      ],
      advantages: [
        'True RMS measurement',
        'Accurate non-sinusoidal readings',
        'Enhanced measurement capabilities',
        'Professional accuracy',
        'IP 67 protection',
        'Versatile functionality'
      ]
    },
    dmm240: {
      id: 'dmm240',
      model: 'DMM 240',
      subtitle: 'Advanced Digital Multimeter',
      image: '/multimeter/DMM 240.png',
      images: [
        '/multimeter/DMM 240.png',
        '/multimeter/DMM 240.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '40,000 counts LCD',
      accuracy: '±0.2%',
      price: 'Contact for pricing',
      description: 'The DMM 240 offers high-resolution measurements with 40,000 count display and advanced features including peak measurement for comprehensive electrical analysis.',
      keyFeatures: [
        'Display: 40,000 counts backlit with bargraph',
        'Measurement Type: TRMS',
        'Calibre Selection: Autorange/Manual',
        'Audible continuity & Diode test',
        'Automatic shutdown (deactivatable)',
        'Relative mode – MIN, MAX, PEAK (1 ms)',
        'High-resolution measurements',
        'IP 67 protection rating',
        'Peak hold function',
        'Advanced data logging',
        'Enhanced measurement speed',
        'Professional-grade accuracy'
      ],
      technicalSpecs: {
        'Voltage Range': 'V(AC/DC): 400 mV to 1000 V',
        'AC Bandwidth': 'Up to 1 kHz',
        'Current Range': 'A (AC/DC): 400 μA to 10 A',
        'Resistance': '400 Ω to 40 MΩ',
        'Frequency': 'Up to 100 MHz',
        'Capacitance': 'Up to 40 mF',
        'Temperature': 'Up to 1000°C',
        'Display': '40,000 counts backlit with bargraph',
        'Measurement Type': 'TRMS',
        'Peak Measurement': '1 ms',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '200 x 100 x 60 mm',
        'Weight': '0.6 kg',
        'Basic Accuracy': '±0.2% (VDC)',
        'Resolution': 'Up to 0.01 mV'
      },
      applications: [
        'Advanced electrical testing',
        'High-precision measurements',
        'Industrial equipment diagnostics',
        'Research and development',
        'Quality control testing',
        'Professional electrical work'
      ],
      advantages: [
        'High-resolution display',
        'Advanced measurement capabilities',
        'Peak measurement function',
        'Superior accuracy',
        'Professional-grade features',
        'Robust construction'
      ]
    },
    ca5273: {
      id: 'ca5273',
      model: 'CA 5273',
      subtitle: 'CA Advanced Series Multimeter',
      image: '/multimeter/CA 5273.png',
      images: [
        '/multimeter/CA 5273.png',
        '/multimeter/CA 5273.png' // Add more images when available
      ],
      voltage: '1000V AC/DC',
      measurement: '2 x 6,000 counts LCD',
      accuracy: '±0.5%',
      price: 'Contact for pricing',
      description: 'The CA 5273 features dual display with automatic AC/DC detection for enhanced measurement efficiency and professional electrical testing applications.',
      keyFeatures: [
        'Display: 2 x 6,000 counts with backlighting & bargraph',
        'Autorange/Deactivatable',
        'Automatic AC/DC detection',
        'Audible continuity & Diode test',
        'Hold function',
        'Temperature measurement up to 1,200°C',
        'IP 54 protection rating',
        'Professional construction',
        'Dual measurement capability',
        'Enhanced user interface',
        'Advanced safety features',
        'Comprehensive measurement range'
      ],
      technicalSpecs: {
        'Voltage Range': 'V (AC/DC): 600 mV to 1000 V',
        'AC Bandwidth': 'Up to 3 kHz',
        'Current Range': 'A (AC/DC): 20 mA to 10 A',
        'Resistance': 'Up to 60 MΩ',
        'Frequency': 'Up to 50 kHz',
        'Capacitance': 'Up to 60 mF',
        'Temperature': 'Up to 1,200°C',
        'Display': '2 x 6,000 counts with backlighting',
        'AC/DC Detection': 'Automatic',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 54',
        'Dimensions': '210 x 105 x 65 mm',
        'Weight': '0.7 kg',
        'Safety Standard': 'IEC 61010-1 CAT III 1000V',
        'Display Update Rate': '3 readings per second'
      },
      applications: [
        'Professional electrical testing',
        'Industrial facility monitoring',
        'Power system analysis',
        'Electrical installation verification',
        'Maintenance and troubleshooting',
        'Temperature monitoring'
      ],
      advantages: [
        'Dual display capability',
        'Automatic AC/DC detection',
        'Enhanced measurement range',
        'Professional accuracy',
        'Temperature measurement',
        'Reliable performance'
      ]
    },
    mtx3291: {
      id: 'mtx3291',
      model: 'MTX 3291',
      subtitle: 'High Resolution Multimeter',
      image: '/multimeter/MTX 3291.png',
      images: [
        '/multimeter/MTX 3291.png',
        '/multimeter/MTX 3291.png' // Add more images when available
      ],
      voltage: '1000V AC/DC/AC+DC',
      measurement: '60,000 counts LCD',
      accuracy: '±0.2%',
      price: 'Contact for pricing',
      description: 'The MTX 3291 provides high-resolution measurements with USB communication and PC interface for advanced data logging and analysis applications.',
      keyFeatures: [
        'Display: 60,000 counts backlight digital monochrome',
        'TRMS value measurement',
        'Audible continuity detection (600 Ω SIGNAL 30 Ω ≤ 5 V)',
        'Diode test capability',
        'HOLD/Auto-HOLD functions',
        'Communication: USB interface',
        'PC interface software included',
        'IP 67 protection rating',
        'High-resolution display',
        'Advanced data logging',
        'Professional connectivity',
        'Enhanced measurement precision'
      ],
      technicalSpecs: {
        'Voltage Range': 'V (AC/DC/AC+DC): Up to 1000 V',
        'AC & AC+DC Bandwidth': '100 kHz',
        'Current Range': 'A (AC/DC/AC+DC): Up to 20 A',
        'Frequency': 'Up to 600 kHz',
        'Resistance': 'Up to 60 MΩ',
        'Capacitance': 'Up to 60 mF',
        'Temperature': 'Up to 800°C',
        'Display': '60,000 counts backlit digital',
        'Communication': 'USB interface',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '220 x 110 x 70 mm',
        'Weight': '0.8 kg',
        'Data Storage': 'Internal memory for logging',
        'PC Software': 'Included for data analysis'
      },
      applications: [
        'High-precision measurements',
        'Data logging applications',
        'Research and development',
        'Quality control testing',
        'Industrial maintenance',
        'Professional electrical work'
      ],
      advantages: [
        'High-resolution display',
        'USB communication',
        'PC interface capability',
        'Advanced measurement functions',
        'Professional accuracy',
        'Data logging capability'
      ]
    },
    ca5292: {
      id: 'ca5292',
      model: 'CA 5292',
      subtitle: 'Professional Digital Multimeter',
      image: '/multimeter/CA 5292.png',
      images: [
        '/multimeter/CA 5292.png',
        '/multimeter/CA 5292.png' // Add more images when available
      ],
      voltage: '1000V AC/DC/AC+DC',
      measurement: '100,000 count color display',
      accuracy: '±0.03% (VDC)',
      price: 'Contact for pricing',
      description: 'The CA 5292 delivers industry-leading performance with color graphical display, trend analysis, and comprehensive data logging for the most demanding professional applications.',
      keyFeatures: [
        '4 x 100,000 count colour graphical display with backlighting',
        'TRMS value measurement',
        'Audible continuity detection (1000 Ω/SIGNAL < 20 Ω < 3.5 V) & Diode test',
        'Display of trends, multiple parameters & 600 Hz waveform',
        'HOLD/Auto-HOLD functions',
        'Memory: 10,000 measurements',
        'Communication: USB, Bluetooth (optional)',
        'PC interface software included',
        'IP 67 protection rating',
        'Advanced graphical interface',
        'Real-time waveform display',
        'Professional data analysis'
      ],
      technicalSpecs: {
        'Voltage Range': 'V (AC/DC/AC+DC): Up to 1000 V',
        'VDC Accuracy': '0.03%',
        'VAC Accuracy': '0.3%',
        'AC & AC+DC Bandwidth': 'Up to 100 kHz',
        'Current Range': 'A (AC/DC/AC+DC): Up to 20 A',
        'Current Accuracy': '0.08% (DC) & 0.3% (AC/AC+DC)',
        'AC Current Bandwidth': '50 kHz',
        'Frequency': 'Up to 5 MHz',
        'Resistance': 'Up to 100 MΩ',
        'Capacitance': 'Up to 10 mF',
        'Temperature': 'Up to 1200 °C (with K thermocouple)',
        'Display': '4 x 100,000 count colour graphical',
        'Memory': '10,000 measurements',
        'Communication': 'USB, Optional Bluetooth',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'IP Rating': 'IP 67',
        'Dimensions': '250 x 120 x 80 mm',
        'Weight': '1.0 kg'
      },
      applications: [
        'Premium electrical testing',
        'Research and development',
        'High-precision measurements',
        'Data logging and analysis',
        'Professional electrical work',
        'Quality control testing'
      ],
      advantages: [
        'Color graphical display',
        'Industry-leading accuracy',
        'Advanced data logging',
        'Trend analysis capability',
        'Professional-grade features',
        'Comprehensive connectivity'
      ]
    },
    f65: {
      id: 'f65',
      model: 'F65',
      subtitle: 'RMS Leakage Current Clamp',
      image: '/multimeter/F65.png',
      images: [
        '/multimeter/F65.png',
        '/multimeter/F65.png' // Add more images when available
      ],
      voltage: '600V AC/DC',
      measurement: '10,000 counts LCD',
      accuracy: '1.2% ± 5 cts',
      price: 'Contact for pricing',
      description: 'The F65 is a specialized RMS leakage current clamp meter designed for measuring small AC currents and leakage currents in electrical installations with IEC 61557-13 compliance.',
      keyFeatures: [
        'Clamping diameter: 28 mm',
        'Display: 10,000 counts backlit LCD',
        'Hold & Auto power off functions',
        'Audible Continuity (Buzzer 35 Ω)',
        'Complied IEC 61557-13 standard',
        'Leakage current measurement capability',
        'Compact and portable design',
        'Professional construction',
        'High sensitivity measurement',
        'Safety compliance testing',
        'Precision clamp design',
        'Enhanced measurement accuracy'
      ],
      technicalSpecs: {
        'AC Current Range': '60 mA to 100 A',
        'AC Current Accuracy': '1.2% ± 5 cts',
        'Voltage Range': '600 V AC/DC',
        'Resistance & Continuity': 'at 1 kΩ',
        'Frequency Range': '5 Hz to 1 kHz',
        'Clamping Diameter': '28 mm',
        'Display': '10,000 counts backlit LCD',
        'IEC Compliance': 'IEC 61557-13',
        'Operating Temperature': '-10°C to +50°C',
        'Storage Temperature': '-20°C to +60°C',
        'Dimensions': '200 x 80 x 40 mm',
        'Weight': '0.3 kg',
        'Resolution': '0.01 mA (60 mA range)',
        'Safety Category': 'CAT III 600V',
        'Battery Type': '2 x 1.5V AA alkaline'
      },
      applications: [
        'Leakage current measurement',
        'Electrical installation testing',
        'Safety compliance testing',
        'Preventive maintenance',
        'Electrical troubleshooting',
        'Ground fault detection'
      ],
      advantages: [
        'Specialized leakage measurement',
        'IEC 61557-13 compliance',
        'Compact clamp design',
        'High accuracy',
        'Professional reliability',
        'Easy operation'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/digital-multimeters');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Digital Multimeter`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic similar to OscilloscopeProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('tft') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('data')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('connectivity') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi') || feature.toLowerCase().includes('bluetooth')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('channels')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('measurement') || feature.toLowerCase().includes('accuracy') || feature.toLowerCase().includes('trms')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('cat') || feature.toLowerCase().includes('ip') || feature.toLowerCase().includes('protection')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('analysis') || feature.toLowerCase().includes('bargraph') || feature.toLowerCase().includes('trend')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

<div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Digital Multimeters
              </h1>
              <p className="typography-h4 text-black">
                Professional Electrical Measurement Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => navigate(`/measure/digital-multimeters/product/${prod.id}`)}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/digital-multimeters')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Voltage Range</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Accuracy</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full max-w-xs">
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="w-full h-auto object-contain"
                      style={{ 
                        maxHeight: '200px',
                        maxWidth: '200px',
                        background: 'transparent',
                        mixBlendMode: 'multiply',
                        filter: 'brightness(1.1) contrast(1.1)',
                        opacity: '0.95'
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section - Expandable Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
                >
                  {/* Header */}
                  <div className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900">Key Features</h2>
                  </div>
                  
                  {/* Content Area - Flex Grow */}
                  <div className="flex-1 flex flex-col">
                    {/* Preview Content - Always Visible */}
                    <div className="px-6 pb-6 space-y-4 flex-1">
                      {product.keyFeatures.slice(0, 6).map((feature, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                        >
                          <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <FeatureIcon feature={feature} />
                          </div>
                          <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                    
                    {/* Expandable Content - Additional Features */}
                    {product.keyFeatures.length > 6 && (
                      <AnimatePresence>
                        {featuresExpanded && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6 space-y-4 border-t border-gray-100 pt-4">
                              {product.keyFeatures.slice(6).map((feature, index) => (
                                <motion.div
                                  key={index + 6}
                                  initial={{ opacity: 0, x: -20 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ duration: 0.4, delay: index * 0.05 }}
                                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                                >
                                  <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <FeatureIcon feature={feature} />
                                  </div>
                                  <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                  
                  {/* Show More/Less Button - Always at Bottom */}
                  {product.keyFeatures.length > 6 && (
                    <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                      <button
                        onClick={() => setFeaturesExpanded(!featuresExpanded)}
                        className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                      >
                        {featuresExpanded ? (
                          <>
                            <span>Show Less</span>
                            <ChevronDown className="h-4 w-4" />
                          </>
                        ) : (
                          <>
                            <span>Show {product.keyFeatures.length - 6} More Features</span>
                            <ChevronRight className="h-4 w-4" />
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Technical Specifications Section - Expandable Card */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
              >
                {/* Header */}
                <div className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900">Technical Specifications</h2>
                </div>
                
                {/* Content Area - Flex Grow */}
                <div className="flex-1 flex flex-col">
                  {/* Preview Content - Always Visible */}
                  <div className="px-6 pb-6 flex-1">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <tbody>
                          {Object.entries(product.technicalSpecs).slice(0, 6).map(([key, value], index) => (
                            <motion.tr
                              key={key}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.4, delay: index * 0.05 }}
                              className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                              }`}
                            >
                              <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                {key}
                              </td>
                              <td className="py-4 px-4 text-gray-700 font-medium">
                                {value}
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Expandable Content - Additional Specifications */}
                  {Object.entries(product.technicalSpecs).length > 6 && (
                    <AnimatePresence>
                      {specsExpanded && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6 border-t border-gray-100 pt-4">
                            <div className="overflow-x-auto">
                              <table className="w-full">
                                <tbody>
                                  {Object.entries(product.technicalSpecs).slice(6).map(([key, value], index) => (
                                    <motion.tr
                                      key={key}
                                      initial={{ opacity: 0, x: -20 }}
                                      animate={{ opacity: 1, x: 0 }}
                                      transition={{ duration: 0.4, delay: index * 0.05 }}
                                      className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                                        index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                                      }`}
                                    >
                                      <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                                        {key}
                                      </td>
                                      <td className="py-4 px-4 text-gray-700 font-medium">
                                        {value}
                                      </td>
                                    </motion.tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
                
                {/* Show More/Less Button - Always at Bottom */}
                {Object.entries(product.technicalSpecs).length > 6 && (
                  <div className="px-6 pb-6 border-t border-gray-100 pt-4 mt-auto">
                    <button
                      onClick={() => setSpecsExpanded(!specsExpanded)}
                      className="w-full py-3 px-4 text-black hover:text-black font-semibold text-sm flex items-center justify-center gap-2 transition-all duration-200 rounded-lg hover:bg-yellow-50 border border-yellow-200 hover:border-yellow-300"
                    >
                      {specsExpanded ? (
                        <>
                          <span>Show Less</span>
                          <ChevronDown className="h-4 w-4" />
                        </>
                      ) : (
                        <>
                          <span>Show {Object.entries(product.technicalSpecs).length - 6} More Specifications</span>
                          <ChevronRight className="h-4 w-4" />
                        </>
                      )}
                    </button>
                  </div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on digital multimeter solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <Phone className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default MultimeterProduct;