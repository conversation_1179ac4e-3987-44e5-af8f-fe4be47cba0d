import React, { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';

interface CarouselProps {
  images: string[];
  className?: string;
  theme?: 'green' | 'yellow';
}

const Carousel: React.FC<PropsWithChildren<CarouselProps>> = ({ images, className = '', theme = 'green' }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollTo = useCallback((index: number) => {
    if (emblaApi) emblaApi.scrollTo(index);
  }, [emblaApi]);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  // Theme colors
  const themeColors = {
    green: {
      arrow: '#059669',
      border: 'border-green-200',
      dotActive: 'bg-green-600',
      dotInactive: 'bg-green-200'
    },
    yellow: {
      arrow: '#D97706',
      border: 'border-yellow-200',
      dotActive: 'bg-yellow-600',
      dotInactive: 'bg-yellow-200'
    }
  };

  const colors = themeColors[theme];

  useEffect(() => {
    if (!emblaApi) return;
    const onSelect = () => setSelectedIndex(emblaApi.selectedScrollSnap());
    emblaApi.on('select', onSelect);
    onSelect();
    return () => { emblaApi.off('select', onSelect); };
  }, [emblaApi]);

  // Auto-scroll every 10 seconds
  useEffect(() => {
    if (!emblaApi) return;
    const interval = setInterval(() => {
      const nextIndex = (selectedIndex + 1) % images.length;
      emblaApi.scrollTo(nextIndex);
    }, 10000); // 10 seconds
    return () => clearInterval(interval);
  }, [emblaApi, selectedIndex, images.length]);

  return (
    <div className={`embla ${className} relative`}>
      {/* Arrow Buttons - Outside the image */}
      {images.length > 1 && (
        <>
          <button
            className={`absolute -left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg border ${colors.border} flex items-center justify-center transition-all duration-200`}
            style={{ minWidth: 40, minHeight: 40 }}
            onClick={scrollPrev}
            aria-label="Previous slide"
            type="button"
          >
            {/* Left Arrow SVG */}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 15L8 10L13 5" stroke={colors.arrow} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          <button
            className={`absolute -right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 hover:bg-white rounded-full p-2 shadow-lg border ${colors.border} flex items-center justify-center transition-all duration-200`}
            style={{ minWidth: 40, minHeight: 40 }}
            onClick={scrollNext}
            aria-label="Next slide"
            type="button"
          >
            {/* Right Arrow SVG */}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7 5L12 10L7 15" stroke={colors.arrow} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </>
      )}
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {images.map((src, idx) => (
            <div className="embla__slide" key={idx}>
              <img src={src} alt="carousel" className="w-full h-full object-contain" />
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-center mt-2 gap-2 hidden sm:flex">
        {images.map((_, idx) => (
          <button
            key={idx}
            className={`
              w-2 h-2 md:w-2.5 md:h-2.5 rounded-full
              ${selectedIndex === idx ? colors.dotActive : colors.dotInactive}
            `}
            onClick={() => scrollTo(idx)}
            aria-label={`Go to slide ${idx + 1}`}
          />
        ))}
      </div>
      <style>{`
        .embla { position: relative; height: 100%; }
        .embla__viewport { overflow: hidden; width: 100%; height: 100%; }
        .embla__container { display: flex; height: 100%; }
        .embla__slide { flex: 0 0 100%; min-width: 0; height: 100%; }
      `}</style>
    </div>
  );
};

export default Carousel;
